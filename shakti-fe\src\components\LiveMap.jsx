import { useEffect, useRef } from 'react'
import L from 'leaflet'

// Fix for default markers in Leaflet with Vite
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

const LiveMap = () => {
  const mapRef = useRef(null)
  const mapInstanceRef = useRef(null)

  // Mock drone positions with lat/lng coordinates
  const dronePositions = [
    { id: 1, lat: 40.7128, lng: -74.0060, status: 'active' },
    { id: 2, lat: 40.7589, lng: -73.9851, status: 'active' },
    { id: 3, lat: 40.7505, lng: -73.9934, status: 'active' },
    { id: 4, lat: 40.7282, lng: -73.7949, status: 'active' },
    { id: 5, lat: 40.6892, lng: -74.0445, status: 'active' },
    { id: 6, lat: 40.7831, lng: -73.9712, status: 'active' },
    { id: 7, lat: 40.7614, lng: -73.9776, status: 'active' },
    { id: 8, lat: 40.7489, lng: -73.9680, status: 'active' },
    { id: 9, lat: 40.7061, lng: -74.0087, status: 'active' },
    { id: 10, lat: 40.7580, lng: -73.9855, status: 'active' },
    { id: 11, lat: 40.7505, lng: -73.9934, status: 'active' }
  ]

  useEffect(() => {
    if (mapRef.current && !mapInstanceRef.current) {
      // Initialize the map
      mapInstanceRef.current = L.map(mapRef.current).setView([40.7128, -74.0060], 12)

      // Add tile layer
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(mapInstanceRef.current)

      // Create custom drone icon
      const droneIcon = L.divIcon({
        className: 'custom-drone-marker',
        html: `
          <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center shadow-lg border-2 border-white transform rotate-45">
            <svg class="w-4 h-4 text-white transform -rotate-45" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C10.9 2 10 2.9 10 4V6H8C6.9 6 6 6.9 6 8V10C6 11.1 6.9 12 8 12H10V14C10 15.1 10.9 16 12 16S14 15.1 14 14V12H16C17.1 12 18 11.1 18 10V8C18 6.9 17.1 6 16 6H14V4C14 2.9 13.1 2 12 2M4 8C2.9 8 2 8.9 2 10S2.9 12 4 12 6 11.1 6 10 5.1 8 4 8M20 8C18.9 8 18 8.9 18 10S18.9 12 20 12 22 11.1 22 10 21.1 8 20 8M12 18C10.9 18 10 18.9 10 20S10.9 22 12 22 14 21.1 14 20 13.1 18 12 18Z" />
              <circle cx="12" cy="10" r="1.5" fill="white"/>
            </svg>
          </div>
        `,
        iconSize: [32, 32],
        iconAnchor: [16, 16]
      })

      // Add drone markers
      dronePositions.forEach((drone) => {
        L.marker([drone.lat, drone.lng], { icon: droneIcon })
          .addTo(mapInstanceRef.current)
          .bindPopup(`
            <div class="text-center">
              <strong>Drone #${drone.id}</strong><br>
              Status: <span class="text-green-600 font-medium">${drone.status}</span><br>
              Lat: ${drone.lat.toFixed(4)}<br>
              Lng: ${drone.lng.toFixed(4)}
            </div>
          `)
      })
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove()
        mapInstanceRef.current = null
      }
    }
  }, [])

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <h2 className="text-lg font-semibold text-gray-900">Live Drone Tracking on map</h2>
        </div>

        <div className="flex items-center space-x-3">
          {/* Dropdown Filter */}
          <select className="px-3 py-1 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
            <option>Active Drones - 11</option>
            <option>All Drones</option>
            <option>Inactive Drones</option>
          </select>

          {/* Full Screen Button */}
          <button className="inline-flex items-center px-3 py-1 border border-gray-200 rounded-md text-sm font-medium text-gray-600 bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 transition-colors duration-200">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
            View full screen
          </button>
        </div>
      </div>

      {/* Map Container */}
      <div
        ref={mapRef}
        className="w-full rounded-lg overflow-hidden border border-gray-200"
        style={{ height: '400px' }}
      />
    </div>
  )
}

export default LiveMap
