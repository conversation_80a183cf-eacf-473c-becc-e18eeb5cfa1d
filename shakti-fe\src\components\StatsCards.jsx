const StatsCards = () => {
  const stats = [
    {
      title: 'Drones In Inventory',
      value: '194',
      change: '+24% from last month',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C10.9 2 10 2.9 10 4V6H8C6.9 6 6 6.9 6 8V10C6 11.1 6.9 12 8 12H10V14C10 15.1 10.9 16 12 16S14 15.1 14 14V12H16C17.1 12 18 11.1 18 10V8C18 6.9 17.1 6 16 6H14V4C14 2.9 13.1 2 12 2M4 8C2.9 8 2 8.9 2 10S2.9 12 4 12 6 11.1 6 10 5.1 8 4 8M20 8C18.9 8 18 8.9 18 10S18.9 12 20 12 22 11.1 22 10 21.1 8 20 8M12 18C10.9 18 10 18.9 10 20S10.9 22 12 22 14 21.1 14 20 13.1 18 12 18Z" />
        </svg>
      ),
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600',
      textColor: 'text-green-600'
    },
    {
      title: 'Registered Organizations',
      value: '128',
      change: '+12% from last month',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 7V3H2v18h20V7H12zM6 19H4v-2h2v2zm0-4H4v-2h2v2zm0-4H4V9h2v2zm0-4H4V5h2v2zm4 12H8v-2h2v2zm0-4H8v-2h2v2zm0-4H8V9h2v2zm0-4H8V5h2v2zm10 12h-8v-2h2v-2h-2v-2h2v-2h-2V9h8v10z"/>
        </svg>
      ),
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600',
      textColor: 'text-blue-600'
    },
    {
      title: 'Deployed Drones',
      value: '39',
      change: '+19% from last month',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C10.9 2 10 2.9 10 4V6H8C6.9 6 6 6.9 6 8V10C6 11.1 6.9 12 8 12H10V14C10 15.1 10.9 16 12 16S14 15.1 14 14V12H16C17.1 12 18 11.1 18 10V8C18 6.9 17.1 6 16 6H14V4C14 2.9 13.1 2 12 2M4 8C2.9 8 2 8.9 2 10S2.9 12 4 12 6 11.1 6 10 5.1 8 4 8M20 8C18.9 8 18 8.9 18 10S18.9 12 20 12 22 11.1 22 10 21.1 8 20 8M12 18C10.9 18 10 18.9 10 20S10.9 22 12 22 14 21.1 14 20 13.1 18 12 18Z" />
          <circle cx="12" cy="10" r="2" fill="white"/>
          <path d="M7 15L12 12L17 15" stroke="white" strokeWidth="1.5" fill="none"/>
        </svg>
      ),
      bgColor: 'bg-red-50',
      iconColor: 'text-red-600',
      textColor: 'text-red-600'
    },
    {
      title: 'Maintenance',
      value: '27',
      change: '+12% from last month',
      icon: (
        <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
          <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/>
        </svg>
      ),
      bgColor: 'bg-yellow-50',
      iconColor: 'text-yellow-600',
      textColor: 'text-yellow-600'
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {stats.map((stat, index) => (
        <div
          key={index}
          className={`${stat.bgColor} rounded-lg p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200`}
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
              <p className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</p>
              <p className={`text-xs font-medium ${stat.textColor}`}>
                {stat.change}
              </p>
            </div>
            <div className={`${stat.iconColor} ml-3`}>
              {stat.icon}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default StatsCards
